/**
 * Main result interface for skill matching analysis
 */
export interface SkillMatchAnalysis {
  directMatches: DirectSkillMatch[];
  transferableSkills: TransferableSkillMatch[];
  summary: {
    totalJobSkills: number;
    directMatchCount: number;
    transferableMatchCount: number;
    coveragePercentage: number;
  };
}

/**
 * Represents a direct skill match between resume and job requirements
 */
export interface DirectSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  matchType: 'exact' | 'synonym' | 'keyword';
  sourceResume?: number; // Optional for introspection
}

/**
 * Represents a transferable skill match with AI-determined confidence rating
 */
export interface TransferableSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  confidenceRating: 1 | 2 | 3;
  reasoning: string;
  sourceResume?: number; // Optional for introspection
}

/**
 * Configuration options for skill matching analysis
 */
export interface SkillMatchOptions {
  includeSourceResume?: boolean; // For introspection
  maxTransferableSkills?: number; // Limit AI processing
  confidenceThreshold?: 1 | 2 | 3; // Minimum confidence to include
}

/**
 * Internal interface for consolidated skills from multiple resume variations
 */
export interface ConsolidatedSkill {
  name: string;
  level?: string;
  keywords: string[];
  sourceResumes: number[]; // Track which resumes contributed this skill
}
