/**
 * Supported model variants available through Cloudflare AI
 */
export const CloudflareAIModelVariant = {
  /** Mistral 7B Instruct model - smaller, faster model suitable for most tasks */
  MISTRAL_7B_INSTRUCT: '@cf/mistral/mistral-7b-instruct-v0.2',
  /** Mistral Small 3.1 24B Instruct model - larger, more capable model */
  MISTRAL_SMALL: '@cf/mistral/mistral-small-3.1-24b-instruct',
} as const;

/**
 * Type representing the available model variants
 */
export type CloudflareAIModelVariant =
  (typeof CloudflareAIModelVariant)[keyof typeof CloudflareAIModelVariant];

/**
 * Default model variant to use when none is specified
 */
export const DEFAULT_MODEL_VARIANT: CloudflareAIModelVariant =
  CloudflareAIModelVariant.MISTRAL_7B_INSTRUCT;

/**
 * Model-specific configuration and limits
 */
export const MODEL_LIMITS = {
  [CloudflareAIModelVariant.MISTRAL_7B_INSTRUCT]: {
    maxTokens: 8192,
    defaultMaxTokens: 2048,
    family: 'mistral',
  },
  [CloudflareAIModelVariant.MISTRAL_SMALL]: {
    maxTokens: 32768,
    defaultMaxTokens: 4096,
    family: 'mistral',
  },
} as const;

/**
 * Get the default max tokens for a given model variant
 */
export function getDefaultMaxTokens(variant: CloudflareAIModelVariant): number {
  return MODEL_LIMITS[variant].defaultMaxTokens;
}

/**
 * Get the maximum tokens limit for a given model variant
 */
export function getMaxTokensLimit(variant: CloudflareAIModelVariant): number {
  return MODEL_LIMITS[variant].maxTokens;
}

/**
 * Check if a model variant is valid
 */
export function isValidModelVariant(
  variant: string
): variant is CloudflareAIModelVariant {
  return Object.values(CloudflareAIModelVariant).includes(
    variant as CloudflareAIModelVariant
  );
}

/**
 * Get the model family for a given variant
 */
export function getModelFamily(variant: CloudflareAIModelVariant): string {
  return MODEL_LIMITS[variant].family;
}
