import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { describe, expect, it, vi } from 'vitest';
import { CloudflareAIChatModel } from './cloudflare-ai-chat-model';
import {
  CloudflareAIAPIError,
  CloudflareAIConfigError,
} from './errors';
import { CloudflareAIModelVariant } from './model-variants';

// Mock AI binding
const createMockAiBinding = () => ({
  run: vi.fn(),
});

describe('CloudflareAIChatModel', () => {
  describe('constructor', () => {
    it('should create instance with valid configuration', () => {
      const aiBinding = createMockAiBinding();
      const model = new CloudflareAIChatModel({
        aiBinding,
      });

      expect(model).toBeInstanceOf(CloudflareAIChatModel);
      expect(model._llmType()).toBe('cloudflare-ai');
    });

    it('should use default values when optional parameters are not provided', () => {
      const aiBinding = createMockAiBinding();
      const model = new CloudflareAIChatModel({
        aiBinding,
      });

      const identifyingParams = model._identifyingParams();
      expect(identifyingParams.modelVariant).toBe(
        CloudflareAIModelVariant.MISTRAL_7B_INSTRUCT
      );
      expect(identifyingParams.maxTokens).toBe(2048);
      expect(identifyingParams.debug).toBe(false);
    });

    it('should accept custom configuration values', () => {
      const aiBinding = createMockAiBinding();
      const model = new CloudflareAIChatModel({
        aiBinding,
        modelVariant: CloudflareAIModelVariant.MISTRAL_SMALL,
        maxTokens: 4000,
        temperature: 0.7,
        topP: 0.9,
        timeout: 30000,
        debug: true,
      });

      const identifyingParams = model._identifyingParams();
      expect(identifyingParams.modelVariant).toBe(
        CloudflareAIModelVariant.MISTRAL_SMALL
      );
      expect(identifyingParams.maxTokens).toBe(4000);
      expect(identifyingParams.temperature).toBe(0.7);
      expect(identifyingParams.topP).toBe(0.9);
      expect(identifyingParams.timeout).toBe(30000);
      expect(identifyingParams.debug).toBe(true);
    });

    it('should throw CloudflareAIConfigError when aiBinding is missing', () => {
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding: null as any,
        });
      }).toThrow(CloudflareAIConfigError);
    });

    it('should throw CloudflareAIConfigError when aiBinding is invalid', () => {
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding: {} as any,
        });
      }).toThrow(CloudflareAIConfigError);
    });

    it('should throw CloudflareAIConfigError for invalid model variant', () => {
      const aiBinding = createMockAiBinding();
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          modelVariant: 'invalid-model' as any,
        });
      }).toThrow(CloudflareAIConfigError);
    });

    it('should throw CloudflareAIConfigError for invalid maxTokens', () => {
      const aiBinding = createMockAiBinding();

      // Test negative maxTokens
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          maxTokens: -100,
        });
      }).toThrow(CloudflareAIConfigError);

      // Test zero maxTokens
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          maxTokens: 0,
        });
      }).toThrow(CloudflareAIConfigError);

      // Test non-integer maxTokens
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          maxTokens: 100.5,
        });
      }).toThrow(CloudflareAIConfigError);

      // Test maxTokens exceeding model limit
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          modelVariant: CloudflareAIModelVariant.MISTRAL_7B_INSTRUCT,
          maxTokens: 10000, // Exceeds 8192 limit
        });
      }).toThrow(CloudflareAIConfigError);
    });

    it('should throw CloudflareAIConfigError for invalid temperature', () => {
      const aiBinding = createMockAiBinding();

      // Test temperature below 0
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          temperature: -0.1,
        });
      }).toThrow(CloudflareAIConfigError);

      // Test temperature above 2
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          temperature: 2.1,
        });
      }).toThrow(CloudflareAIConfigError);
    });

    it('should throw CloudflareAIConfigError for invalid topP', () => {
      const aiBinding = createMockAiBinding();

      // Test topP below 0
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          topP: -0.1,
        });
      }).toThrow(CloudflareAIConfigError);

      // Test topP above 1
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          topP: 1.1,
        });
      }).toThrow(CloudflareAIConfigError);
    });

    it('should throw CloudflareAIConfigError for invalid timeout', () => {
      const aiBinding = createMockAiBinding();

      // Test negative timeout
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          timeout: -1000,
        });
      }).toThrow(CloudflareAIConfigError);

      // Test zero timeout
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          timeout: 0,
        });
      }).toThrow(CloudflareAIConfigError);

      // Test non-integer timeout
      expect(() => {
        new CloudflareAIChatModel({
          aiBinding,
          timeout: 1000.5,
        });
      }).toThrow(CloudflareAIConfigError);
    });
  });

  describe('_generate method', () => {
    it('should throw error for empty messages array', async () => {
      const aiBinding = createMockAiBinding();
      const model = new CloudflareAIChatModel({ aiBinding });

      await expect(model._generate([])).rejects.toThrow();
    });

    it('should successfully generate response', async () => {
      const aiBinding = createMockAiBinding();
      const mockResponse = {
        success: true,
        result: {
          response: 'Hello, world!',
          success: true,
        },
      };
      
      aiBinding.run.mockResolvedValue(mockResponse);
      
      const model = new CloudflareAIChatModel({ aiBinding });
      const messages = [new HumanMessage('Hello')];
      
      const result = await model._generate(messages);
      
      expect(result.generations).toHaveLength(1);
      expect(result.generations[0].text).toBe('Hello, world!');
      expect(result.generations[0].message).toBeInstanceOf(AIMessage);
    });

    it('should handle API errors', async () => {
      const aiBinding = createMockAiBinding();
      const mockResponse = {
        success: false,
        errors: [{ code: 500, message: 'Internal server error' }],
      };
      
      aiBinding.run.mockResolvedValue(mockResponse);
      
      const model = new CloudflareAIChatModel({ aiBinding });
      const messages = [new HumanMessage('Hello')];
      
      await expect(model._generate(messages)).rejects.toThrow(CloudflareAIAPIError);
    });

    it('should handle timeout', async () => {
      const aiBinding = createMockAiBinding();
      aiBinding.run.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 2000)));
      
      const model = new CloudflareAIChatModel({ 
        aiBinding,
        timeout: 1000,
      });
      const messages = [new HumanMessage('Hello')];
      
      await expect(model._generate(messages)).rejects.toThrow(CloudflareAIAPIError);
    });
  });
});