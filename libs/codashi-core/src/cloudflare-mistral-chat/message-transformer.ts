import type { BaseMessage } from '@langchain/core/messages';
import { CloudflareAIError } from './errors';
import type { CloudflareMessage } from './types';

/**
 * Error thrown when message transformation fails
 */
export class MessageTransformationError extends CloudflareAIError {
  constructor(message: string, cause?: unknown) {
    super(`Message transformation error: ${message}`, cause);
    this.name = 'MessageTransformationError';
  }
}

/**
 * Maps LangChain message roles to Cloudflare AI roles
 */
function mapMessageRole(role: string): 'system' | 'user' | 'assistant' {
  switch (role) {
    case 'system':
      return 'system';
    case 'human':
      return 'user';
    case 'ai':
      return 'assistant';
    default:
      throw new MessageTransformationError(
        `Unsupported message role: ${role}. Supported roles are: system, human (user), ai (assistant)`
      );
  }
}

/**
 * Extracts and validates message content from a BaseMessage
 */
function extractMessageContent(message: BaseMessage): string {
  if (typeof message.content === 'string') {
    return message.content;
  }

  if (Array.isArray(message.content)) {
    // Handle complex content arrays by extracting text content
    const textContent = message.content
      .filter(
        (item) =>
          typeof item === 'object' &&
          item !== null &&
          'type' in item &&
          item.type === 'text'
      )
      .map((item) => (item as { text: string }).text)
      .join(' ');

    if (textContent.trim()) {
      return textContent.trim();
    }

    throw new MessageTransformationError(
      'Complex message content arrays are not supported. Only text content is allowed.'
    );
  }

  throw new MessageTransformationError(
    `Unsupported message content type: ${typeof message.content}. Expected string or text-only array.`
  );
}

/**
 * Transforms a single LangChain BaseMessage to Cloudflare AI format
 */
export function transformMessage(message: BaseMessage): CloudflareMessage {
  if (!message) {
    throw new MessageTransformationError('Message cannot be null or undefined');
  }

  // Extract and map the role
  const langchainRole = message._getType();
  const role = mapMessageRole(langchainRole);

  // Extract and validate the content
  const content = extractMessageContent(message);
  if (!content.trim()) {
    throw new MessageTransformationError('Message content cannot be empty');
  }

  return {
    role,
    content: content.trim(),
  };
}

/**
 * Transforms an array of LangChain BaseMessages to Cloudflare AI format
 */
export function transformMessages(
  messages: BaseMessage[]
): CloudflareMessage[] {
  if (!Array.isArray(messages)) {
    throw new MessageTransformationError('Messages must be an array');
  }

  if (messages.length === 0) {
    throw new MessageTransformationError('Messages array cannot be empty');
  }

  try {
    return messages.map((message, index) => {
      try {
        return transformMessage(message);
      } catch (error) {
        throw new MessageTransformationError(
          `Failed to transform message at index ${index}: ${
            error instanceof Error ? error.message : String(error)
          }`,
          error
        );
      }
    });
  } catch (error) {
    if (error instanceof MessageTransformationError) {
      throw error;
    }
    throw new MessageTransformationError(
      'Failed to transform messages array',
      error
    );
  }
}

/**
 * Validates that the transformed messages are compatible with Cloudflare AI
 */
export function validateTransformedMessages(
  messages: CloudflareMessage[]
): void {
  if (messages.length === 0) {
    throw new MessageTransformationError(
      'Transformed messages array cannot be empty'
    );
  }

  // Check for consecutive messages with the same role (which might cause issues)
  for (let i = 1; i < messages.length; i++) {
    if (
      messages[i].role === messages[i - 1].role &&
      messages[i].role !== 'system'
    ) {
      // Allow multiple system messages but warn about consecutive user/assistant messages
      console.warn(
        `Warning: Consecutive messages with role '${
          messages[i].role
        }' detected at indices ${
          i - 1
        } and ${i}. This might not be optimal for conversation flow.`
      );
    }
  }

  // Validate that all messages have non-empty content
  messages.forEach((message, index) => {
    if (!message.content.trim()) {
      throw new MessageTransformationError(
        `Message at index ${index} has empty content`
      );
    }
  });
}

/**
 * Complete transformation pipeline that converts LangChain messages to Cloudflare format
 * with validation
 */
export function transformAndValidateMessages(
  messages: BaseMessage[]
): CloudflareMessage[] {
  const transformed = transformMessages(messages);
  validateTransformedMessages(transformed);
  return transformed;
}
