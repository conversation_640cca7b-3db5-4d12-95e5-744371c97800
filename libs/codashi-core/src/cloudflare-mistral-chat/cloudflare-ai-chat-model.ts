import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { BaseMessage } from '@langchain/core/messages';
import { AIMessage, ChatMessageChunk } from '@langchain/core/messages';
import { ChatGenerationChunk, ChatR<PERSON>ult } from '@langchain/core/outputs';

import { CloudflareAIAPIError, CloudflareAIConfigError } from './errors';
import { transformAndValidateMessages } from './message-transformer';
import {
  CloudflareAIModelVariant,
  DEFAULT_MODEL_VARIANT,
  getDefaultMaxTokens,
  getMaxTokensLimit,
  isValidModelVariant,
} from './model-variants';
import type {
  CloudflareAIChatModelConfig,
  CloudflareAIRequest,
  CloudflareAIResponse,
  CloudflareAIStreamChunk,
  CloudflareAIStreamResponse,
} from './types';

/**
 * CloudflareAIChatModel - A Lang<PERSON>hain BaseChatModel implementation
 * that integrates with Cloudflare AI bindings for various AI models.
 *
 * This implementation provides enhanced control over token limits and model configuration
 * specific to Cloudflare's AI platform, addressing limitations in the existing
 * @langchain/cloudflare package.
 */
export class CloudflareAIChatModel extends BaseChatModel {
  /** Cloudflare AI binding for API communication */
  private readonly aiBinding: Ai;

  /** Selected AI model variant */
  private readonly modelVariant: CloudflareAIModelVariant;

  /** Maximum number of tokens to generate */
  private readonly maxTokens: number;

  /** Temperature for response generation */
  private readonly temperature?: number;

  /** Top-p for nucleus sampling */
  private readonly topP?: number;

  /** Request timeout in milliseconds */
  private readonly timeout?: number;

  /** Debug logging flag */
  private readonly debug: boolean;

  /**
   * Creates a new CloudflareAIChatModel instance
   *
   * @param config Configuration object for the model
   * @throws {CloudflareAIConfigError} When configuration is invalid
   */
  constructor(config: CloudflareAIChatModelConfig) {
    super(config);

    // Validate required configuration
    this.validateConfig(config);

    // Initialize core properties with validation
    this.aiBinding = config.aiBinding;
    this.modelVariant = config.modelVariant ?? DEFAULT_MODEL_VARIANT;
    this.maxTokens = this.validateAndSetMaxTokens(config.maxTokens);
    this.temperature = this.validateTemperature(config.temperature);
    this.topP = this.validateTopP(config.topP);
    this.timeout = this.validateTimeout(config.timeout);
    this.debug = config.debug ?? false;
  }

  /**
   * Validates the configuration object
   */
  private validateConfig(config: CloudflareAIChatModelConfig): void {
    if (!config.aiBinding) {
      throw new CloudflareAIConfigError(
        'aiBinding is required. Ensure you are running in a Cloudflare Workers environment with AI binding configured.'
      );
    }

    if (typeof config.aiBinding !== 'object' || !('run' in config.aiBinding)) {
      throw new CloudflareAIConfigError(
        'aiBinding must be a valid Cloudflare AI binding object with a run method.'
      );
    }

    if (config.modelVariant && !isValidModelVariant(config.modelVariant)) {
      throw new CloudflareAIConfigError(
        `Invalid model variant: ${
          config.modelVariant
        }. Supported variants: ${Object.values(CloudflareAIModelVariant).join(
          ', '
        )}`
      );
    }
  }

  /**
   * Validates and sets the max tokens value
   */
  private validateAndSetMaxTokens(maxTokens?: number): number {
    if (maxTokens === undefined) {
      return getDefaultMaxTokens(this.modelVariant);
    }

    if (!Number.isInteger(maxTokens) || maxTokens <= 0) {
      throw new CloudflareAIConfigError('maxTokens must be a positive integer');
    }

    const limit = getMaxTokensLimit(this.modelVariant);
    if (maxTokens > limit) {
      throw new CloudflareAIConfigError(
        `maxTokens (${maxTokens}) exceeds the limit for ${this.modelVariant} (${limit})`
      );
    }

    return maxTokens;
  }

  /**
   * Validates temperature parameter
   */
  private validateTemperature(temperature?: number): number | undefined {
    if (temperature === undefined) return undefined;

    if (typeof temperature !== 'number' || temperature < 0 || temperature > 2) {
      throw new CloudflareAIConfigError(
        'temperature must be a number between 0 and 2'
      );
    }

    return temperature;
  }

  /**
   * Validates top-p parameter
   */
  private validateTopP(topP?: number): number | undefined {
    if (topP === undefined) return undefined;

    if (typeof topP !== 'number' || topP < 0 || topP > 1) {
      throw new CloudflareAIConfigError(
        'topP must be a number between 0 and 1'
      );
    }

    return topP;
  }

  /**
   * Validates timeout parameter
   */
  private validateTimeout(timeout?: number): number | undefined {
    if (timeout === undefined) return undefined;

    if (!Number.isInteger(timeout) || timeout <= 0) {
      throw new CloudflareAIConfigError(
        'timeout must be a positive integer (milliseconds)'
      );
    }

    return timeout;
  }

  // Required BaseChatModel methods
  override async _generate(messages: BaseMessage[]): Promise<ChatResult> {
    try {
      const transformedMessages = transformAndValidateMessages(messages);

      const request: CloudflareAIRequest = {
        messages: transformedMessages,
        max_tokens: this.maxTokens,
      };

      if (this.temperature !== undefined) {
        request.temperature = this.temperature;
      }
      if (this.topP !== undefined) {
        request.top_p = this.topP;
      }

      if (this.debug) {
        console.log('[CloudflareAIChatModel] API Request:', {
          model: this.modelVariant,
          request,
          timestamp: new Date().toISOString(),
        });
      }

      const response = await this.makeAPICall(request);

      if (this.debug) {
        console.log('[CloudflareAIChatModel] API Response:', {
          response,
          timestamp: new Date().toISOString(),
        });
      }

      return this.transformResponse(response);
    } catch (error) {
      if (error instanceof CloudflareAIAPIError) {
        throw error;
      }

      throw new CloudflareAIAPIError(
        `Failed to generate response: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  override async *_streamResponseChunks(
    messages: BaseMessage[]
  ): AsyncGenerator<ChatGenerationChunk> {
    try {
      const transformedMessages = transformAndValidateMessages(messages);

      const request: CloudflareAIRequest = {
        messages: transformedMessages,
        max_tokens: this.maxTokens,
        stream: true,
      };

      if (this.temperature !== undefined) {
        request.temperature = this.temperature;
      }
      if (this.topP !== undefined) {
        request.top_p = this.topP;
      }

      if (this.debug) {
        console.log('[CloudflareAIChatModel] Streaming Request:', {
          model: this.modelVariant,
          request,
          timestamp: new Date().toISOString(),
        });
      }

      try {
        const stream = await this.makeStreamingAPICall(request);
        yield* this.processStreamingResponse(stream);
      } catch (streamingError) {
        const shouldFallback =
          this.shouldFallbackToNonStreaming(streamingError);

        if (shouldFallback) {
          if (this.debug) {
            console.log(
              '[CloudflareAIChatModel] Streaming failed, falling back to non-streaming:',
              {
                error:
                  streamingError instanceof Error
                    ? streamingError.message
                    : String(streamingError),
                timestamp: new Date().toISOString(),
              }
            );
          }

          const nonStreamingRequest = { ...request };
          delete nonStreamingRequest.stream;

          const response = await this.makeAPICall(nonStreamingRequest);
          const chatResult = this.transformResponse(response);

          if (chatResult.generations.length > 0) {
            const generation = chatResult.generations[0];
            yield new ChatGenerationChunk({
              message: new ChatMessageChunk({
                content: generation.text,
                role: 'assistant',
              }),
              text: generation.text,
              generationInfo: chatResult.llmOutput,
            });
          }
        } else {
          throw streamingError;
        }
      }
    } catch (error) {
      if (error instanceof CloudflareAIAPIError) {
        throw error;
      }

      throw new CloudflareAIAPIError(
        `Failed to stream response: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  // Model identification methods
  override _llmType(): string {
    return 'cloudflare-ai';
  }

  override _identifyingParams() {
    return {
      modelVariant: this.modelVariant,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      topP: this.topP,
      timeout: this.timeout,
      debug: this.debug,
    } as const;
  }

  /**
   * Determines if we should fallback to non-streaming mode
   */
  private shouldFallbackToNonStreaming(error: unknown): boolean {
    if (error instanceof CloudflareAIAPIError) {
      // Don't fallback for auth errors, rate limits, etc.
      if (error.statusCode === 401 || error.statusCode === 429) {
        return false;
      }
    }

    // Fallback for streaming-specific errors
    return true;
  }

  /**
   * Makes a streaming API call to Cloudflare AI
   */
  private async makeStreamingAPICall(
    request: CloudflareAIRequest
  ): Promise<ReadableStream | AsyncIterable<CloudflareAIStreamResponse>> {
    try {
      const apiCallPromise = this.aiBinding.run(this.modelVariant, request);

      let response: CloudflareAIResponse | ReadableStream;

      if (this.timeout) {
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(
              new CloudflareAIAPIError(
                `Streaming request timed out after ${this.timeout}ms`,
                408
              )
            );
          }, this.timeout);
        });

        response = await Promise.race([apiCallPromise, timeoutPromise]);
      } else {
        response = await apiCallPromise;
      }

      // If the response is a stream, return it
      if (response && typeof response === 'object' && 'getReader' in response) {
        return response as ReadableStream;
      }

      // If it's a regular response, convert to async iterable
      const regularResponse = response as CloudflareAIResponse;
      return this.convertResponseToAsyncIterable(regularResponse);
    } catch (error) {
      if (error instanceof CloudflareAIAPIError) {
        throw error;
      }

      throw new CloudflareAIAPIError(
        `Streaming API call failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Converts a regular response to an async iterable for consistency
   */
  private async *convertResponseToAsyncIterable(
    response: CloudflareAIResponse
  ): AsyncIterable<CloudflareAIStreamResponse> {
    yield {
      result: response.result,
      success: response.success,
    };
  }

  /**
   * Processes streaming response and yields chunks
   */
  private async *processStreamingResponse(
    stream: ReadableStream | AsyncIterable<CloudflareAIStreamResponse>
  ): AsyncGenerator<ChatGenerationChunk> {
    if ('getReader' in stream) {
      yield* this.processReadableStream(stream);
    } else {
      yield* this.processAsyncIterable(stream);
    }
  }

  /**
   * Processes ReadableStream response
   */
  private async *processReadableStream(
    stream: ReadableStream
  ): AsyncGenerator<ChatGenerationChunk> {
    const reader = stream.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter((line) => line.trim());

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6)) as CloudflareAIStreamChunk;
              if (data.data) {
                yield new ChatGenerationChunk({
                  message: new ChatMessageChunk({
                    content: data.data,
                    role: 'assistant',
                  }),
                  text: data.data,
                });
              }
            } catch (parseError) {
              // Skip malformed chunks
              continue;
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Processes AsyncIterable response
   */
  private async *processAsyncIterable(
    iterable: AsyncIterable<CloudflareAIStreamResponse>
  ): AsyncGenerator<ChatGenerationChunk> {
    for await (const chunk of iterable) {
      if (chunk.result?.response) {
        yield new ChatGenerationChunk({
          message: new ChatMessageChunk({
            content: chunk.result.response,
            role: 'assistant',
          }),
          text: chunk.result.response,
        });
      }
    }
  }

  /**
   * Makes an API call to Cloudflare AI with timeout and error handling
   */
  private async makeAPICall(
    request: CloudflareAIRequest
  ): Promise<CloudflareAIResponse> {
    try {
      const apiCallPromise = this.aiBinding.run(this.modelVariant, request);

      if (this.timeout) {
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(
              new CloudflareAIAPIError(
                `Request timed out after ${this.timeout}ms`,
                408
              )
            );
          }, this.timeout);
        });

        return (await Promise.race([
          apiCallPromise,
          timeoutPromise,
        ])) as CloudflareAIResponse;
      }

      return (await apiCallPromise) as CloudflareAIResponse;
    } catch (error) {
      if (error instanceof CloudflareAIAPIError) {
        throw error;
      }

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new CloudflareAIAPIError(
          'Network error: Unable to reach Cloudflare AI API'
        );
      }

      throw new CloudflareAIAPIError(
        `Unexpected API error: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Transforms the Cloudflare AI response to LangChain ChatResult format
   */
  private transformResponse(response: CloudflareAIResponse): ChatResult {
    if (!response || typeof response !== 'object') {
      throw new CloudflareAIAPIError(
        'Invalid response: Response is not an object'
      );
    }

    if (!response.success) {
      const errorMessage = response.errors?.length
        ? response.errors.map((err) => `${err.code}: ${err.message}`).join(', ')
        : 'Unknown error occurred';
      throw new CloudflareAIAPIError(`API request failed: ${errorMessage}`);
    }

    if (!response.result?.response) {
      throw new CloudflareAIAPIError(
        'Invalid response: Missing result.response field'
      );
    }

    const message = new AIMessage(response.result.response);

    return {
      generations: [
        {
          text: response.result.response,
          message,
        },
      ],
      llmOutput: {
        modelVariant: this.modelVariant,
        maxTokens: this.maxTokens,
        temperature: this.temperature,
        topP: this.topP,
      },
    };
  }
}
