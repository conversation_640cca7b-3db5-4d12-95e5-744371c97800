/**
 * Base error class for CloudflareAI errors
 */
export class CloudflareAIError extends Error {
  constructor(message: string, public cause?: unknown) {
    super(message);
    this.name = 'CloudflareAIError';

    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CloudflareAIError);
    }
  }
}

/**
 * Error thrown when there are configuration issues
 */
export class CloudflareAIConfigError extends CloudflareAIError {
  constructor(message: string) {
    super(`Configuration error: ${message}`);
    this.name = 'CloudflareAIConfigError';

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CloudflareAIConfigError);
    }
  }
}

/**
 * Error thrown when API calls fail
 */
export class CloudflareAIAPIError extends CloudflareAIError {
  constructor(message: string, public statusCode?: number) {
    super(`API error: ${message}`);
    this.name = 'Cloud<PERSON>lareAIAPIError';

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CloudflareAIAPIError);
    }
  }
}
