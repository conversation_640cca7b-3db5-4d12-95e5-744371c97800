import {
  AIMessage,
  HumanMessage,
  SystemMessage,
} from '@langchain/core/messages';
import { describe, expect, it, vi } from 'vitest';
import {
  MessageTransformationError,
  transformAndValidateMessages,
  transformMessage,
  transformMessages,
  validateTransformedMessages,
} from './message-transformer';
import type { CloudflareMessage } from './types';

describe('Message Transformer', () => {
  describe('transformMessage', () => {
    it('should transform a HumanMessage correctly', () => {
      const message = new HumanMessage('Hello, how are you?');
      const result = transformMessage(message);

      expect(result).toEqual({
        role: 'user',
        content: 'Hello, how are you?',
      });
    });

    it('should transform an AIMessage correctly', () => {
      const message = new AIMessage('I am doing well, thank you!');
      const result = transformMessage(message);

      expect(result).toEqual({
        role: 'assistant',
        content: 'I am doing well, thank you!',
      });
    });

    it('should transform a SystemMessage correctly', () => {
      const message = new SystemMessage('You are a helpful assistant.');
      const result = transformMessage(message);

      expect(result).toEqual({
        role: 'system',
        content: 'You are a helpful assistant.',
      });
    });

    it('should trim whitespace from message content', () => {
      const message = new HumanMessage('  Hello world  ');
      const result = transformMessage(message);

      expect(result.content).toBe('Hello world');
    });

    it('should throw error for null message', () => {
      expect(() => transformMessage(null as any)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessage(null as any)).toThrow(
        'Message cannot be null or undefined'
      );
    });

    it('should throw error for undefined message', () => {
      expect(() => transformMessage(undefined as any)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessage(undefined as any)).toThrow(
        'Message cannot be null or undefined'
      );
    });

    it('should throw error for empty content', () => {
      const message = new HumanMessage('');
      expect(() => transformMessage(message)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessage(message)).toThrow(
        'Message content cannot be empty'
      );
    });

    it('should throw error for whitespace-only content', () => {
      const message = new HumanMessage('   ');
      expect(() => transformMessage(message)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessage(message)).toThrow(
        'Message content cannot be empty'
      );
    });

    it('should handle complex content arrays with text content', () => {
      // Create a message and manually set complex content array
      const message = new HumanMessage('placeholder');
      (message as any).content = [
        { type: 'text', text: 'Hello' },
        { type: 'text', text: 'world' },
      ];

      const result = transformMessage(message);
      expect(result.content).toBe('Hello world');
    });

    it('should throw error for complex content arrays without text', () => {
      // Create a message and manually set complex content array without text
      const message = new HumanMessage('placeholder');
      (message as any).content = [
        { type: 'image', url: 'http://example.com/image.jpg' },
      ];

      expect(() => transformMessage(message)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessage(message)).toThrow(
        'Complex message content arrays are not supported'
      );
    });

    it('should throw error for unsupported content type', () => {
      // Create a message with invalid content by modifying it after creation
      const message = new HumanMessage('test');
      (message as any).content = 123; // Force invalid content type

      expect(() => transformMessage(message)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessage(message)).toThrow(
        'Unsupported message content type: number'
      );
    });
  });

  describe('transformMessages', () => {
    it('should transform multiple messages correctly', () => {
      const messages = [
        new SystemMessage('You are helpful.'),
        new HumanMessage('Hello!'),
        new AIMessage('Hi there!'),
      ];

      const result = transformMessages(messages);

      expect(result).toEqual([
        { role: 'system', content: 'You are helpful.' },
        { role: 'user', content: 'Hello!' },
        { role: 'assistant', content: 'Hi there!' },
      ]);
    });

    it('should throw error for non-array input', () => {
      expect(() => transformMessages('not an array' as any)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessages('not an array' as any)).toThrow(
        'Messages must be an array'
      );
    });

    it('should throw error for empty array', () => {
      expect(() => transformMessages([])).toThrow(MessageTransformationError);
      expect(() => transformMessages([])).toThrow(
        'Messages array cannot be empty'
      );
    });

    it('should provide detailed error for invalid message in array', () => {
      const messages = [
        new HumanMessage('Valid message'),
        new HumanMessage(''), // Invalid empty message
      ];

      expect(() => transformMessages(messages)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessages(messages)).toThrow(
        'Failed to transform message at index 1'
      );
    });

    it('should handle transformation errors gracefully', () => {
      const messages = [
        new HumanMessage('Valid message'),
        null as any, // Invalid null message
      ];

      expect(() => transformMessages(messages)).toThrow(
        MessageTransformationError
      );
      expect(() => transformMessages(messages)).toThrow(
        'Failed to transform message at index 1'
      );
    });
  });

  describe('validateTransformedMessages', () => {
    it('should pass validation for valid messages', () => {
      const messages: CloudflareMessage[] = [
        { role: 'system', content: 'You are helpful.' },
        { role: 'user', content: 'Hello!' },
        { role: 'assistant', content: 'Hi there!' },
      ];

      expect(() => validateTransformedMessages(messages)).not.toThrow();
    });

    it('should throw error for empty array', () => {
      expect(() => validateTransformedMessages([])).toThrow(
        MessageTransformationError
      );
      expect(() => validateTransformedMessages([])).toThrow(
        'Transformed messages array cannot be empty'
      );
    });

    it('should throw error for message with empty content', () => {
      const messages: CloudflareMessage[] = [{ role: 'user', content: '' }];

      expect(() => validateTransformedMessages(messages)).toThrow(
        MessageTransformationError
      );
      expect(() => validateTransformedMessages(messages)).toThrow(
        'Message at index 0 has empty content'
      );
    });

    it('should throw error for message with whitespace-only content', () => {
      const messages: CloudflareMessage[] = [{ role: 'user', content: '   ' }];

      expect(() => validateTransformedMessages(messages)).toThrow(
        MessageTransformationError
      );
      expect(() => validateTransformedMessages(messages)).toThrow(
        'Message at index 0 has empty content'
      );
    });

    it('should warn about consecutive user messages', () => {
      const consoleSpy = vi
        .spyOn(console, 'warn')
        .mockImplementation(() => undefined);

      const messages: CloudflareMessage[] = [
        { role: 'user', content: 'First message' },
        { role: 'user', content: 'Second message' },
      ];

      validateTransformedMessages(messages);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "Consecutive messages with role 'user' detected"
        )
      );

      consoleSpy.mockRestore();
    });

    it('should warn about consecutive assistant messages', () => {
      const consoleSpy = vi
        .spyOn(console, 'warn')
        .mockImplementation(() => undefined);

      const messages: CloudflareMessage[] = [
        { role: 'assistant', content: 'First response' },
        { role: 'assistant', content: 'Second response' },
      ];

      validateTransformedMessages(messages);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "Consecutive messages with role 'assistant' detected"
        )
      );

      consoleSpy.mockRestore();
    });

    it('should allow consecutive system messages without warning', () => {
      const consoleSpy = vi
        .spyOn(console, 'warn')
        .mockImplementation(() => undefined);

      const messages: CloudflareMessage[] = [
        { role: 'system', content: 'First system message' },
        { role: 'system', content: 'Second system message' },
      ];

      validateTransformedMessages(messages);

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('transformAndValidateMessages', () => {
    it('should transform and validate messages successfully', () => {
      const messages = [
        new SystemMessage('You are helpful.'),
        new HumanMessage('Hello!'),
        new AIMessage('Hi there!'),
      ];

      const result = transformAndValidateMessages(messages);

      expect(result).toEqual([
        { role: 'system', content: 'You are helpful.' },
        { role: 'user', content: 'Hello!' },
        { role: 'assistant', content: 'Hi there!' },
      ]);
    });

    it('should throw transformation error for invalid input', () => {
      const messages = [
        new HumanMessage(''), // Invalid empty message
      ];

      expect(() => transformAndValidateMessages(messages)).toThrow(
        MessageTransformationError
      );
    });

    it('should throw validation error for invalid transformed messages', () => {
      // This test ensures the validation step catches issues by creating a message
      // that will pass transformation but fail validation
      const message = new HumanMessage('Valid message');
      // Manually create an invalid transformed message to test validation
      const invalidTransformed = [{ role: 'user' as const, content: '' }];

      expect(() => validateTransformedMessages(invalidTransformed)).toThrow(
        MessageTransformationError
      );
    });
  });

  describe('MessageTransformationError', () => {
    it('should create error with correct name and message', () => {
      const error = new MessageTransformationError('Test error');

      expect(error.name).toBe('MessageTransformationError');
      expect(error.message).toBe('Message transformation error: Test error');
    });

    it('should preserve cause when provided', () => {
      const cause = new Error('Original error');
      const error = new MessageTransformationError('Test error', cause);

      expect(error.cause).toBe(cause);
    });
  });
});
