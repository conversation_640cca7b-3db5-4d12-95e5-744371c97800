import type { CloudflareAIModelVariant } from './model-variants';

/**
 * Configuration interface for CloudflareAIChatModel
 */
export interface CloudflareAIChatModelConfig {
  // Required
  /** Cloudflare AI binding - handles authentication automatically when used within Cloudflare Workers */
  aiBinding: Ai;

  // Model configuration
  /** AI model variant to use */
  modelVariant?: CloudflareAIModelVariant;
  /** Maximum number of tokens to generate */
  maxTokens?: number;

  // Optional LLM parameters
  /** Controls randomness in generation (0.0 to 2.0) */
  temperature?: number;
  /** Controls nucleus sampling (0.0 to 1.0) */
  topP?: number;

  // Operational settings
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Enable debug logging */
  debug?: boolean;

  // LangChain compatibility
  /** Enable streaming responses */
  streaming?: boolean;
  /** Tags for the model instance */
  tags?: string[];
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Message format expected by Cloudflare AI
 */
export interface CloudflareMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * Request format for Cloudflare AI API
 */
export interface CloudflareAIRequest {
  messages: CloudflareMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

/**
 * Response format from Cloudflare AI API
 */
export interface CloudflareAIResponse {
  result: {
    response: string;
    success: boolean;
  };
  success: boolean;
  errors?: Array<{ code: number; message: string }>;
}

/**
 * Streaming response chunk from Cloudflare AI API
 */
export interface CloudflareAIStreamChunk {
  data: string;
}

/**
 * Streaming response format from Cloudflare AI API
 */
export interface CloudflareAIStreamResponse {
  result: {
    response: string;
    success: boolean;
  };
  success: boolean;
}

/**
 * Cloudflare AI binding interface (minimal definition)
 * This represents the AI binding available in Cloudflare Workers
 */
declare global {
  interface Ai {
    run(
      model: string,
      options: CloudflareAIRequest
    ): Promise<CloudflareAIResponse | ReadableStream>;
  }
}
